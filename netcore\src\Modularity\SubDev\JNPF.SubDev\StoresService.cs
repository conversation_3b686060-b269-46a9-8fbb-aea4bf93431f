using JNPF.ClayObject;
using JNPF.Common.Configuration;
using JNPF.Common.Core.Manager;
using JNPF.Common.Core.Manager.Files;
using JNPF.Common.Core.Security;
using JNPF.Common.Enum;
using JNPF.Common.Filter;
using JNPF.Common.Helper;
using JNPF.Common.Models.NPOI;
using JNPF.Common.Security;
using JNPF.DataEncryption;
using JNPF.DependencyInjection;
using JNPF.DynamicApiController;
using JNPF.FriendlyException;
using JNPF.SubDev.Entitys;
using JNPF.SubDev.Entitys.Dto.MyStores;
using JNPF.SubDev.Interfaces;
using JNPF.Systems.Entitys.Permission;
using JNPF.Systems.Entitys.System;
using JNPF.Systems.Interfaces.Permission;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SqlSugar;

namespace JNPF.SubDev;

/// <summary>
/// 业务实现：门店基础信息.
/// </summary>
[ApiDescriptionSettings(Tag = "SubDev", Name = "Stores", Order = 200)]
[Route("api/SubDev/[controller]")]
public class StoresService : IStoresService, IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<StoresEntity> _repository;

    private readonly ISqlSugarRepository<ProvinceEntity> _province;
    private readonly IFileManager _fileManager;
    private readonly ISqlSugarRepository<StoresEntity> _stores;
    private readonly ISqlSugarRepository<OrganizeEntity> _organize;
    private readonly IOrganizeService _organizeIService;
    private readonly ISqlSugarRepository<MyopencloseEntity> _openclose;
    private readonly ISqlSugarRepository<MyyeartargetEntity> _yearTarget; //年度销售目标
    private readonly ISqlSugarRepository<MyinvitedclientEntity> _invitedClient; //月度邀请人数
    private readonly ISqlSugarRepository<MystoreselldataEntity> _selldata; //各门店销售明细
    private readonly ISqlSugarRepository<MysamplesEntity> _samples; //出样表
    private readonly ISqlSugarRepository<MystorepicEntity> _pic; //出样表




    private readonly MyquerysService _myquerys;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    /// <summary>
    /// 用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    /// 初始化一个<see cref="StoresService"/>类型的新实例.
    /// </summary>
    public StoresService(
        ISqlSugarRepository<StoresEntity> storesRepository,
        ISqlSugarClient context,
        IUserManager userManager, ISqlSugarRepository<ProvinceEntity> province,
        ISqlSugarRepository<StoresEntity> stores, IFileManager fileManager,
        ISqlSugarRepository<OrganizeEntity> organize, ISqlSugarRepository<MyopencloseEntity> openclose,
        IOrganizeService organizeIService, ISqlSugarRepository<MyyeartargetEntity> yearTarget, MyquerysService myquerys, ISqlSugarRepository<MyinvitedclientEntity> invitedClient, ISqlSugarRepository<MystoreselldataEntity> selldata, ISqlSugarRepository<MysamplesEntity> samples, ISqlSugarRepository<MystorepicEntity> pic)
    {
        _repository = storesRepository;
        _db = context.AsTenant();
        _userManager = userManager;
        _province = province;
        _stores = stores;
        _fileManager = fileManager;
        _organize = organize;
        _openclose = openclose;
        _organizeIService = organizeIService;
        _yearTarget = yearTarget;
        _myquerys = myquerys;
        _invitedClient = invitedClient;
        _selldata = selldata;
        _samples = samples;
        _pic = pic;
    }

    /// <summary>
    /// 获取门店基础信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<dynamic> GetInfo(string id)
    {
        var data = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        // if (data is null) return null;
        if (!string.IsNullOrEmpty(data.City))
        {
            return data.Adapt<StoresInfoOutput>();
        }
        else
        {
            var result = new StoresInfoOutput();
            result.id = data.Id;
            result.terminal = data.Terminal;
            result.region = data.Region;
            result.agency = data.Agency;
            result.level1 = data.Level1;
            return result;
        }
    }

    /// <summary>
    /// 获取门店基础信息列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("")]
    public async Task<dynamic> GetList([FromQuery] StoresListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<StoresListOutput>(input.menuId, "id",
                _userManager.UserOrigin.Equals("pc") ? true : true);
        }

        var city = input.city?.Split(',').ToList().Last();
        var sidx = input.sidx == null ? "CreatorTime" : input.sidx;
        var data = await _repository.Context.Queryable<StoresEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.region), it => it.Region.Equals(input.region))
            .WhereIF(!string.IsNullOrEmpty(input.agency), it => it.Agency.Equals(input.agency))
            .WhereIF(!string.IsNullOrEmpty(input.city), it => it.City.Contains(city))
            .WhereIF(!string.IsNullOrEmpty(input.citylevel), it => it.Citylevel.Equals(input.citylevel))
            .WhereIF(!string.IsNullOrEmpty(input.terminal), it => it.Terminal.Contains(input.terminal))
            .WhereIF(!string.IsNullOrEmpty(input.storeaddress), it => it.Storeaddress.Contains(input.storeaddress))
            .WhereIF(!string.IsNullOrEmpty(input.level1), it => it.Level1.Equals(input.level1))
            .WhereIF(!string.IsNullOrEmpty(input.level2), it => it.Level2.Equals(input.level2))
            .WhereIF(!string.IsNullOrEmpty(input.level3), it => it.Level3.Equals(input.level3))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), it =>
                it.Region.Contains(input.keyword)
                || it.Agency.Contains(input.keyword)
                || it.City.Contains(input.keyword)
                || it.Citylevel.Contains(input.keyword)
                || it.Terminal.Contains(input.keyword)
                || it.Storeaddress.Contains(input.keyword)
                || it.Level1.Contains(input.keyword)
                || it.Level2.Contains(input.keyword)
                || it.Level3.Contains(input.keyword)
            )
            .Where(authorizeWhere)
            //.OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.Id)
            .OrderBy(sidx + " " + input.sort) //好像orderbyif语句无效
            .Select(it => new StoresListOutput
            {
                id = it.Id,
                region = it.Region,
                agency = it.Agency,
                signedclient = it.Signedclient,
                distributor = it.Distributor,
                city = it.City,
                citylevel = it.Citylevel,
                terminal = it.Terminal,
                storeaddress = it.Storeaddress,
                storearea = it.Storearea,
                decorationtime = it.Decorationtime,
                openingdate = it.Openingdate,
                level1 = it.Level1,
                level2 = it.Level2,
                level3 = it.Level3,
                contactsr = it.Contactsr,
                contactshopkeeper = it.Contactshopkeeper,
                burn = SqlFunc.IIF(it.Burn == 0, "无", "有"),
                burnextend = it.Burnextend,
                electric = SqlFunc.IIF(it.Electric == 0, "无", "有"),
                electricextend = it.Electricextend,
                water = SqlFunc.IIF(it.Water == 0, "无", "有"),
                waterextend = it.Waterextend,
                kitchen = SqlFunc.IIF(it.Kitchen == 0, "无", "有"),
                kitchenextend = it.Kitchenextend,
                heating = SqlFunc.IIF(it.Heating == 0, "无", "有"),
                heatingextend = it.Heatingextend,
                pumpmultple = SqlFunc.IIF(it.Pumpmultple == 0, "无", "有"),
                pumpoven = SqlFunc.IIF(it.Pumpoven == 0, "无", "有"),
                pumpextend = it.Pumpextend,
                otherextend = it.Otherextend,
                lastModifyTime = it.LastModifyTime,
                lastModifyUserId = it.LastModifyUserId,
                deleted = it.Deleted,
                saleTarget = it.SaleTarget,
                firstdecorationtime = it.Firstdecorationtime,
                firstopeningdate = it.Firstopeningdate,
                enabledMark = it.EnabledMark,
                CreatorTime = it.CreatorTime,
                business = SqlFunc.IIF(it.Business == 0, "无", "有"),
                open = it.Open,
                close = it.Close,
                businessextend = it.Businessextend,
                tel = it.tel
            }).OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort)
            .ToPagedListAsync(input.currentPage, input.pageSize);

        var storeDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.Terminal);
        var organizeDic = await _organize.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.FullName);

        foreach (var item in data.list)
        {
            if (storeDic.ContainsKey(item.terminal))
            {
                item.terminal = (string)storeDic[item.terminal];
            }
        }


        // 获取省份字典
        var provinceDic = await _province.Context.Queryable<ProvinceEntity>()
            .ToDictionaryAsync(i => i.Id, i => i.FullName);

        // 转换城市名称和官网显示状态
        foreach (var item in data.list)
        {
            // 转换城市名称
            var result = new List<string>();
            if (!string.IsNullOrEmpty(item.city))
            {
                try
                {
                    var citys = JsonConvert.DeserializeObject<List<string>>(item.city);

                    if (citys != null)
                    {
                        foreach (var cityItem in citys)
                        {
                            if (provinceDic.ContainsKey(cityItem))
                            {
                                result.Add((string)provinceDic[cityItem]);
                            }
                        }
                    }

                    item.city = string.Join(",", result);
                }
                catch (JsonException ex)
                {
                    // Handle the JSON parsing error gracefully
                    Console.WriteLine($"Error parsing city JSON for store: {item.terminal}. Error: {ex.Message}");
                    // Use the raw value without parsing
                    item.city = "Invalid city data";
                }
            }

            // 转换官网显示状态为文本
            item.enabledMarkText = item.enabledMark == 1 ? "不显示" : "正常";
        }

        return PageResult<StoresListOutput>.SqlSugarPageResult(data);
    }

    /// <summary>
    /// 新建门店基础信息.
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("")]
    public async Task Create([FromBody] StoresCrInput input)
    {
        #region 非管理员不允许创建生活馆或者两通店

        var isManager = _userManager.Roles.Contains("421085870561449925") || _userManager.IsAdministrator; //确定是否管理员
        var checkList = new List<string> { "生活馆", "两通店" };

        if (!isManager && checkList.Contains(input.level1))
        {
            throw new Exception("只有管理员才能创建生活馆或者两通店");
        }

        #endregion

        var entity = input.Adapt<StoresEntity>();
        //数据库表中存在相同的Terminal不允许录入
        var isExist = await _repository.FirstOrDefaultAsync(x => x.Terminal == entity.Terminal);
        if (isExist != null) throw Oops.Oh(ErrorCode.COM1004);

        entity.Id = SnowflakeIdHelper.NextId();
        entity.Terminal = input.terminal.Replace(" ", "");
        entity.CreatorUserId = _userManager.UserId;
        entity.CreatorTime = DateTime.Now;
        if (string.IsNullOrEmpty(entity.Open))
        {
            entity.Open = "10:00";
        }

        if (string.IsNullOrEmpty(entity.Close))
        {
            entity.Close = "17:00";
        }
        // entity.OrganizationId = _userManager.User.OrganizeId;

        // 生成organizeId
        var organizeDic = await GetOrganizeDicTask();

        if (entity.Agency != null && organizeDic.ContainsKey(entity.Agency))
        {
            entity.OrganizationId = organizeDic[entity.Agency];
        }

        var isOk = await _repository.Context.Insertable(entity).IgnoreColumns(ignoreNullColumn: true)
            .ExecuteCommandAsync();
        try
        {
            // 调用增量接口 暂不上线  //todo  微信同步
            await _myquerys.AddOrUpdateStoreInfo(entity.Terminal);
        }
        catch (Exception ex)
        {
            throw new Exception("当前表数据保存成功，但微信同步失败。");
        }

        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1000);
    }

    /// <summary>
    /// 获取组织机构名称和id字典
    /// </summary>
    /// <returns></returns>
    private async Task<Dictionary<string, string>> GetOrganizeDicTask()
    {
        var data = await _organizeIService.GetListAsync();
        var dic = data.ToDictionary(i => i.FullName, i => i.Id);
        return dic;
    }

    /// <summary>
    /// 获取门店基础信息无分页列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    private async Task<dynamic> GetNoPagingList([FromQuery] StoresListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<StoresListOutput>(input.menuId, "id",
                _userManager.UserOrigin.Equals("pc") ? true : false);
        }

        var city = input.city?.Split(',').ToList().Last();
        var sidx = input.sidx == null ? "CreatorTime" : input.sidx;

        var data = await _repository.Context.Queryable<StoresEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.region), it => it.Region.Equals(input.region))
            .WhereIF(!string.IsNullOrEmpty(input.agency), it => it.Agency.Equals(input.agency))
            .WhereIF(!string.IsNullOrEmpty(input.city), it => it.City.Contains(city))
            .WhereIF(!string.IsNullOrEmpty(input.citylevel), it => it.Citylevel.Equals(input.citylevel))
            .WhereIF(!string.IsNullOrEmpty(input.terminal), it => it.Terminal.Contains(input.terminal))
            .WhereIF(!string.IsNullOrEmpty(input.storeaddress), it => it.Storeaddress.Contains(input.storeaddress))
            .WhereIF(!string.IsNullOrEmpty(input.level1), it => it.Level1.Equals(input.level1))
            .WhereIF(!string.IsNullOrEmpty(input.level2), it => it.Level2.Equals(input.level2))
            .WhereIF(!string.IsNullOrEmpty(input.level3), it => it.Level3.Equals(input.level3))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), it =>
                it.Region.Contains(input.keyword)
                || it.Agency.Contains(input.keyword)
                || it.City.Contains(input.keyword)
                || it.Citylevel.Contains(input.keyword)
                || it.Terminal.Contains(input.keyword)
                || it.Storeaddress.Contains(input.keyword)
                || it.Level1.Contains(input.keyword)
                || it.Level2.Contains(input.keyword)
                || it.Level3.Contains(input.keyword)
            )
            .Where(authorizeWhere)
            //.OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.Id)
            .OrderBy(sidx + " " + input.sort) //好像orderbyif语句无效
            .Select(it => new StoresListOutput
            {
                id = it.Id,
                region = it.Region,
                agency = it.Agency,
                signedclient = it.Signedclient,
                distributor = it.Distributor,
                city = it.City,
                citylevel = it.Citylevel,
                terminal = it.Terminal,
                storeaddress = it.Storeaddress,
                storearea = it.Storearea,
                decorationtime = it.Decorationtime,
                openingdate = it.Openingdate,
                level1 = it.Level1,
                level2 = it.Level2,
                level3 = it.Level3,
                contactsr = it.Contactsr,
                contactshopkeeper = it.Contactshopkeeper,
                burn = SqlFunc.IIF(it.Burn == 0, "无", "有"),
                burnextend = it.Burnextend,
                electric = SqlFunc.IIF(it.Electric == 0, "无", "有"),
                electricextend = it.Electricextend,
                water = SqlFunc.IIF(it.Water == 0, "无", "有"),
                waterextend = it.Waterextend,
                kitchen = SqlFunc.IIF(it.Kitchen == 0, "无", "有"),
                kitchenextend = it.Kitchenextend,
                heating = SqlFunc.IIF(it.Heating == 0, "无", "有"),
                heatingextend = it.Heatingextend,
                pumpmultple = SqlFunc.IIF(it.Pumpmultple == 0, "无", "有"),
                pumpoven = SqlFunc.IIF(it.Pumpoven == 0, "无", "有"),
                pumpextend = it.Pumpextend,
                otherextend = it.Otherextend,
                deleted = it.Deleted,
                saleTarget = it.SaleTarget,
                firstdecorationtime = it.Firstdecorationtime,
                firstopeningdate = it.Firstopeningdate,
                enabledMark = it.EnabledMark,
                CreatorTime = it.CreatorTime,
                lastModifyTime = it.LastModifyTime,
                lastModifyUserId = it.LastModifyUserId,
                business = SqlFunc.IIF(it.Business == 0, "无", "有"),
                open = it.Open,
                close = it.Close,
                businessextend = it.Businessextend,
                tel = it.tel

            }).OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort).ToListAsync();

        var storeDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.Terminal);
        var organizeDic = await _organize.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.FullName);

        foreach (var item in data)
        {
            if (storeDic.ContainsKey(item.terminal))
            {
                item.terminal = (string)storeDic[item.terminal];
            }
        }


        // // 获取省份字典
        var provinceDic = await _province.Context.Queryable<ProvinceEntity>()
            .ToDictionaryAsync(i => i.Id, i => i.FullName);

        // 转换城市名称和官网显示状态
        foreach (var item in data)
        {
            // 转换城市名称
            var result = new List<string>();
            if (!string.IsNullOrEmpty(item.city))
            {
                try
                {
                    var citys = JsonConvert.DeserializeObject<List<string>>(item.city);

                    if (citys != null)
                    {
                        foreach (var cityItem in citys)
                        {
                            if (provinceDic.ContainsKey(cityItem))
                            {
                                result.Add((string)provinceDic[cityItem]);
                            }
                        }
                    }

                    item.city = string.Join(",", result);
                }
                catch (JsonException ex)
                {
                    // Handle the JSON parsing error gracefully
                    Console.WriteLine($"Error parsing city JSON for store: {item.terminal}. Error: {ex.Message}");
                    // Use the raw value without parsing
                    item.city = "Invalid city data";
                }
            }

            // 转换官网显示状态为文本
            item.enabledMarkText = item.enabledMark == 1 ? "不显示" : "正常";
        }

        return data;
    }

    /// <summary>
    /// 导出门店基础信息.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("Actions/Export")]
    public async Task<dynamic> Export([FromQuery] StoresListQueryInput input)
    {
        var exportData = new List<StoresListOutput>();
        if (input.dataType == 0)
        {
            exportData = await GetNoPagingList(input);
            // 选择    exportData中lastModifyTime为本月的数据
            exportData = exportData.Where(x =>
                x.lastModifyTime?.Month == DateTime.Now.Month || x.CreatorTime?.Month == DateTime.Now.Month).ToList();
        }
        else
            exportData = await GetNoPagingList(input);


        List<ParamsModel> paramList =
            "[{\"value\":\"大区\",\"field\":\"region\"},{\"value\":\"办事处\",\"field\":\"agency\"},{\"value\":\"平台商【签约客户】\",\"field\":\"signedclient\"},{\"value\":\"零售商【分销商】\",\"field\":\"distributor\"},{\"value\":\"省市区域\",\"field\":\"city\"},{\"value\":\"城市级别\",\"field\":\"citylevel\"},{\"value\":\"终端名称\",\"field\":\"terminal\"},{\"value\":\"详细地址\",\"field\":\"storeaddress\"},{\"value\":\"门店面积\",\"field\":\"storearea\"},{\"value\":\"装修时间\",\"field\":\"decorationtime\"},{\"value\":\"开业时间\",\"field\":\"openingdate\"},{\"value\":\"一级\",\"field\":\"level1\"},{\"value\":\"二级\",\"field\":\"level2\"},{\"value\":\"三级\",\"field\":\"level3\"},{\"value\":\"林内\",\"field\":\"contactsr\"},{\"value\":\"经销商对接人\",\"field\":\"contactshopkeeper\"},{\"value\":\"燃热区域燃热\",\"field\":\"burn\"},{\"value\":\"延米\",\"field\":\"burnextend\"},{\"value\":\"电热区域电热\",\"field\":\"electric\"},{\"value\":\"延米\",\"field\":\"electricextend\"},{\"value\":\"净水区域净水\",\"field\":\"water\"},{\"value\":\"延米\",\"field\":\"waterextend\"},{\"value\":\"厨房区域厨房\",\"field\":\"kitchen\"},{\"value\":\"延米\",\"field\":\"kitchenextend\"},{\"value\":\"采暖区域采暖\",\"field\":\"heating\"},{\"value\":\"延米\",\"field\":\"heatingextend\"},{\"value\":\"热泵\",\"field\":\"pumpmultple\"},{\"value\":\"壁挂炉\",\"field\":\"pumpoven\"},{\"value\":\"延米\",\"field\":\"pumpextend\"},{\"value\":\"其他延米数\",\"field\":\"otherextend\"},{\"value\":\"是否闭店\",\"field\":\"deleted\"},{\"value\":\"官网不显示\",\"field\":\"enabledMarkText\"},{\"value\":\"联系方式\",\"field\":\"tel\"}]"
                .ToList<ParamsModel>();
        ExcelConfig excelconfig = new ExcelConfig();
        excelconfig.FileName = "门店基础信息.xls";
        excelconfig.HeadFont = "微软雅黑";
        excelconfig.HeadPoint = 10;
        excelconfig.IsAllSizeColumn = true;
        excelconfig.ColumnModel = new List<ExcelColumnModel>();
        var cols = input.selectKey.Split(',').ToList();
        cols.Add("deleted");
        foreach (var item in cols)
        {
            var isExist = paramList.Find(p => p.field == item);
            if (isExist != null)
                excelconfig.ColumnModel.Add(
                    new ExcelColumnModel() { Column = isExist.field, ExcelColumn = isExist.value });
        }

        var addPath = FileVariable.TemporaryFilePath + excelconfig.FileName;
        ExcelExportHelper<StoresListOutput>.Export(exportData, excelconfig, addPath);
        var fileName = _userManager.UserId + "|" + addPath + "|xls";
        return new
        {
            name = excelconfig.FileName,
            url = "/api/File/Download?encryption=" + DESCEncryption.Encrypt(fileName, "JNPF")
        };
    }

    /// <summary>
    /// 批量删除门店基础信息.
    /// </summary>
    /// <param name="ids">主键数组.</param>
    /// <returns></returns>
    [HttpPost("batchRemove")]
    public async Task BatchRemove([FromBody] List<string> ids)
    {
        var entitys = await _repository.Context.Queryable<StoresEntity>().In(it => it.Id, ids).ToListAsync();
        if (entitys.Count > 0)
        {
            try
            {
                // 开启事务
                _db.BeginTran();

                // 批量删除门店基础信息
                await _repository.Context.Deleteable<StoresEntity>().In(it => it.Id, ids).ExecuteCommandAsync();

                // 关闭事务
                _db.CommitTran();
            }
            catch (Exception)
            {
                // 回滚事务
                _db.RollbackTran();

                throw Oops.Oh(ErrorCode.COM1002);
            }
        }
    }

    /// <summary>
    /// 更新门店基础信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task Update(string id, [FromBody] StoresUpInput input)
    {
        #region 非管理员不允许修改门店属性为生活馆或者两通店

        var isManager = _userManager.Roles.Contains("421085870561449925") || _userManager.IsAdministrator; //确定是否管理员
        var store = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (store == null) throw Oops.Oh(ErrorCode.COM1003);

        var checkList = new List<string> { "生活馆", "两通店" };

        if (!isManager && store.Level1 != input.level1 && checkList.Contains(input.level1))
        {
            throw new Exception("只有管理员才能修改门店属性为生活馆或者两通店");
        }

        #endregion

        var data = await _repository.Where(i => i.Id != id).Select(i => new { i.Id, i.Terminal }).ToListAsync();
        var obj = data.DistinctBy(i => i.Terminal).ToDictionary(i => i.Terminal, i => i.Id);
        if (obj.ContainsKey(input.terminal)) throw Oops.Oh(ErrorCode.COM1004);
        var entity = input.Adapt<StoresEntity>();
        if (string.IsNullOrEmpty(entity.Open))
        {
            entity.Open = "10:00";
        }

        if (string.IsNullOrEmpty(entity.Close))
        {
            entity.Close = "17:00";
        }
        entity.Terminal = input.terminal.Replace(" ", "");
        entity.LastModifyTime = DateTime.Now;
        entity.LastModifyUserId = _userManager.UserId;
        // 生成organizeId
        var organizeDic = await GetOrganizeDicTask();

        var orgId = organizeDic[entity.Agency];

        if (entity.Agency != null && organizeDic.ContainsKey(entity.Agency))
        {
            entity.OrganizationId = orgId;
        }


        var isOk = await _repository.Context.Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true)
            .ExecuteCommandAsync();

        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1001);

        try
        {
            //更新其他出样表、门店销售明细、年度销售目标、月度邀请人数表中的organizeId
            if (store.Agency != input.agency || store.Terminal != input.terminal)
            {
                //月度邀请
                var invitedClients = _invitedClient.Where(it => it.Terminal == store.Terminal).ToList();
                foreach (var client in invitedClients)
                {
                    client.OrganizationId = orgId;
                    client.Agency = input.agency;
                    client.Region = input.region;
                    client.Terminal = input.terminal;
                }
                //年度目标
                var yearTargets = _yearTarget.Where(it => it.Terminal == store.Terminal).ToList();
                foreach (var yearTarget in yearTargets)
                {
                    yearTarget.OrganizationId = orgId;
                    yearTarget.Agency = input.agency;
                    yearTarget.Region = input.region;
                    yearTarget.Terminal = input.terminal;
                }
                //出样表
                var sample = _samples.FirstOrDefault(it => it.Terminalid == store.Terminal);
                if (sample != null)
                {
                    sample.OrganizationId = orgId;
                    sample.Agency = input.agency;
                    sample.Region = input.region;
                    sample.Terminalid = input.terminal;
                }
                ;

                //门店销售明细
                var details = _selldata.Where(it => it.StoreId == store.Terminal).ToList();
                foreach (var item in details)
                {
                    item.OrganizationId = orgId;
                    item.Agency = input.agency;
                    item.Region = input.region;
                    item.StoreId = input.terminal;
                }
                var pic = _pic.FirstOrDefault(it => it.Terminal == store.Terminal);
                if (pic != null)
                {
                    pic.OrganizationId = orgId;
                    pic.Agency = input.agency;
                    pic.Region = input.region;
                    pic.Terminal = input.terminal;
                }

                //更新数据到数据库
                await _repository.Context.Updateable<MystoreselldataEntity>(details).ExecuteCommandAsync();
                if (sample != null)
                {
                    await _repository.Context.Updateable<MysamplesEntity>(sample).ExecuteCommandAsync();
                }
                await _repository.Context.Updateable<MyyeartargetEntity>(yearTargets).ExecuteCommandAsync();
                await _repository.Context.Updateable<MyinvitedclientEntity>(invitedClients).ExecuteCommandAsync();
                if (pic != null)
                {
                    await _repository.Context.Updateable<MystorepicEntity>(pic).ExecuteCommandAsync();
                }

            }
        }
        catch (Exception ex)
        {
            throw new Exception("当前表数据保存成功，但同步其他表失败。");
        }
        try
        {
            // 调用增量接口 暂不上线  //todo  微信同步
            await _myquerys.AddOrUpdateStoreInfo(entity.Terminal);
        }
        catch (Exception ex)
        {
            throw new Exception("当前表数据保存成功，但微信同步失败。");
        }

    }

    [HttpGet("BactchTest")]
    public async Task BactchTest(CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取所有终端名称
            var names = await _repository.AsQueryable()
                .Select(it => it.Terminal)
                .ToListAsync();

            var num = 1;
            // 遍历处理每个终端
            foreach (var name in names)
            {
                try
                {
                    // 检查是否请求取消
                    cancellationToken.ThrowIfCancellationRequested();

                    // 处理单个终端
                    await _myquerys.AddOrUpdateStoreInfo(name);

                    // 等待1秒
                    await Task.Delay(1000, cancellationToken);

                    // 记录进度
                    num++;
                    if (num>=500)
                    {
                        break;
                    }
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    // 记录单个处理失败，但继续处理其他项
                    throw ex;
                }
            }

        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw;
        }
    }



    /// <summary>
    /// 闭店申请
    /// </summary>
    /// <param name="id"></param>
    /// <param name="operaMonth"></param>
    /// <returns></returns>
    [HttpGet("close")]
    public async Task Close(string id, DateTime operaMonth, string enabledMark)
    {
        var entity = await _stores.FirstOrDefaultAsync(it => it.Id == id);

        // entity.LastModifyTime = DateTime.Now;
        // entity.LastModifyUserId = _userManager.UserId;
        // entity.Deleted = "";

        //创建一条闭店盛情, 如果存在终端名称和年、月一致的数据，那么删除后新建
        await _openclose.DeleteAsync(i =>
            i.Store == entity.Terminal && i.OperationDate.Value.Year == operaMonth.Year &&
            i.OperationDate.Value.Month == operaMonth.Month);

        var data = new MyopencloseEntity();
        data.OperationDate = operaMonth;
        data.Id = SnowflakeIdHelper.NextId();
        data.Store = entity.Terminal;
        data.Region = entity.Region;
        data.Agency = entity.Agency;
        data.Description = entity.Level1;
        data.OperationType = "闭店";
        data.CreatorUserId = _userManager.UserId;
        data.OrganizationId = _userManager.User.OrganizeId;
        data.CreatorTime = DateTime.Now;
        data.IsChecked = "待审批";
        data.EnabledMark = enabledMark;

        await _openclose.InsertAsync(data);
        var isOk = await _repository.Context.Updateable(entity).ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1001);
    }

    /// <summary>
    /// 开店申请
    /// </summary>
    /// <param name="id"></param>
    /// <param name="operaMonth"></param>
    /// <returns></returns>
    [HttpGet("open")]
    public async Task Open(string id, DateTime operaMonth, string enabledMark)
    {
        var entity = await _stores.FirstOrDefaultAsync(it => it.Id == id);

        // entity.LastModifyTime = DateTime.Now;
        // entity.LastModifyUserId = _userManager.UserId;
        // entity.Deleted = "";

        //创建一条闭店盛情, 如果存在终端名称和年、月一致的数据，那么删除后新建
        await _openclose.DeleteAsync(i =>
            i.Store == entity.Terminal && i.OperationDate.Value.Year == operaMonth.Year &&
            i.OperationDate.Value.Month == operaMonth.Month);

        var data = new MyopencloseEntity();
        data.OperationDate = operaMonth;
        data.Id = SnowflakeIdHelper.NextId();
        data.Store = entity.Terminal;
        data.Region = entity.Region;
        data.Agency = entity.Agency;
        data.Description = entity.Level1;
        data.OperationType = "开店";
        data.CreatorUserId = _userManager.UserId;
        data.OrganizationId = _userManager.User.OrganizeId;
        data.CreatorTime = DateTime.Now;
        data.IsChecked = "待审批";
        data.EnabledMark = enabledMark;

        await _openclose.InsertAsync(data);
        var isOk = await _repository.Context.Updateable(entity).ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1001);
    }

    /// <summary>
    /// 删除门店基础信息.
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task Delete(string id)
    {
        var isOk = await _repository.Context.Deleteable<StoresEntity>().Where(it => it.Id.Equals(id))
            .ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1002);
    }

    #region 导入

    /// <summary>
    /// 上传文件.
    /// </summary>
    /// <returns></returns>
    [HttpPost("Uploader")]
    public async Task<dynamic> Uploader(IFormFile file)
    {
        var _filePath = _fileManager.GetPathByType(string.Empty);
        var _fileName = DateTime.Now.ToString("yyyyMMdd") + "_" + SnowflakeIdHelper.NextId() +
                        Path.GetExtension(file.FileName);
        var stream = file.OpenReadStream();
        await _fileManager.UploadFileByType(stream, _filePath, _fileName);
        Console.WriteLine("生成的文件名称为.............................................................");
        Console.WriteLine(_fileName);
        return new { name = _fileName, url = string.Format("/api/File/Image/{0}/{1}", string.Empty, _fileName) };
    }

    /// <summary>
    /// 模板下载.
    /// </summary>
    [HttpGet("TemplateDownload")]
    public async Task<dynamic> TemplateDownload() //需要实现导出模板 
    {
        var filePath = Path.Combine(FileVariable.TemplateFilePath, "stores_info.xlsx"); //模板文件存在服务器上的路径
        var addFilePath = Path.Combine(FileVariable.TemplateFilePath, "门店基本信息.xlsx"); // 保存路径

        if (!(await _fileManager.ExistsFile(addFilePath)))
        {
            var stream = await _fileManager.GetFileStream(filePath);
            await _fileManager.UploadFileByType(stream, FileVariable.TemporaryFilePath, "门店基本信息.xlsx");
        }

        return new
        {
            name = "门店基本信息.xlsx",
            url = "/api/file/Download?encryption=" +
                  DESCEncryption.Encrypt(_userManager.UserId + "|门店基本信息.xlsx", "JNPF")
        };
    }


    /// <summary>
    /// 导入数据.
    /// </summary>
    /// <param name="input">请求参数</param>
    /// <returns></returns>
    [HttpPost("ImportData")] //这里需要较多的修改
    public async Task<dynamic> ImportData_Api([FromBody] MyStoresImportDataInput input)
    {
        var output = new MyStoresImportDataOutput();

        foreach (var item in input.list)
        {
            item.id = SnowflakeIdHelper.NextId();
            try
            {
                var entity = item.Adapt<StoresEntity>();
                entity.CreatorUserId = _userManager.UserId;
                entity.CreatorTime = DateTime.Now;
                entity.OrganizationId = _userManager.User.OrganizeId;
                var isOk = await _repository.Context.Insertable(entity)
                    .ExecuteCommandAsync();

                if (isOk < 1)
                {
                    output.failResult.Add(item);
                    output.fnum++;
                }
                else
                {
                    output.snum++;

                    // 导入成功后进行数据同步
                    try
                    {
                        await _myquerys.AddOrUpdateStoreInfo(entity.Terminal);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"导入门店 {entity.Terminal} 数据同步失败: {ex.Message}");
                        // 注意：这里不抛出异常，避免影响导入操作本身
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                output.failResult.Add(item);
                output.fnum++;
                output.resultType = 1;
            }
        }

        if (output.snum == input.list.Count)
        {
            output.resultType = 0;
        }

        return output;
    }


    /// <summary>
    /// 导入预览.
    /// </summary>
    /// <returns></returns>
    [HttpGet("ImportPreview")]
    public dynamic ImportPreview(string fileName)
    {
        try
        {
            var filePath = FileVariable.TemporaryFilePath;
            var savePath = Path.Combine(filePath, fileName);
            //得到数据
            var excelData = ExcelImportHelper.ToDataTable(savePath);
            foreach (var item in excelData.Columns)
            {
                excelData.Columns[item.ToString()].ColumnName = GetFiledEncode(item.ToString());
            }

            //删除文件
            _fileManager.DeleteFile(savePath);
            //返回结果
            return new { dataRow = excelData };
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            throw Oops.Oh(ErrorCode.D1801);
        }
    }

    /// <summary>
    /// 获取字段编码.
    /// </summary>
    /// <param name="filed"></param>
    /// <returns></returns>
    private string GetFiledEncode(string filed)
    {
        switch (filed)
        {
            case "大区":
                return "region";
            case "办事处":
                return "agency";
            case "平台商【签约客户】":
                return "signedclient";
            case "零售商【分销商】":
                return "distributor";
            case "城市":
                return "city";
            case "城市级别":
                return "citylevel";
            case "终端名称":
                return "terminal";
            case "门店地址":
                return "storeaddress";
            case "门店面积":
                return "storearea";
            case "装修时间":
                return "decorationtime";
            case "开业时间":
                return "openingdate";
            case "一级":
                return "level1";
            case "二级":
                return "level2";
            case "三级":
                return "level3";
            case "四级":
                return "level4";
            case "对接人SR":
                return "contactsr";
            case "对接人零售商":
                return "contactshopkeeper";
            case "燃热区域燃热":
                return "burn";
            case "燃热区域延米":
                return "burnextend";
            case "电热区域电热":
                return "electric";
            case "电热区域延米":
                return "electricextend";
            case "净水区域净水":
                return "water";
            case "净水区域延米":
                return "waterextend";
            case "厨房区域厨房":
                return "kitchen";
            case "厨房区域延米":
                return "kitchenextend";
            case "采暖区域采暖":
                return "heating";
            case "采暖区域延米":
                return "heatingextend";
            case "热泵区域两/三联供.":
                return "pumpmultple";
            case "热泵区域壁挂炉":
                return "pumpoven";
            case "热泵区域延米":
                return "pumpextend";
            case "其他类延米数":
                return "otherextend";

            default:
                return string.Empty;
        }
    }

    /// <summary>
    /// 获取字段名称.
    /// </summary>
    /// <param name="filed"></param>
    /// <returns></returns>
    private string GetFiledName(string filed)
    {
        switch (filed)
        {
            case "region":
                return "大区";
            case "agency":
                return "办事处";
            case "signedclient":
                return "平台商【签约客户】";
            case "distributor":
                return "零售商【分销商】";
            case "city":
                return "城市";
            case "citylevel":
                return "城市级别";
            case "terminal":
                return "终端名称";
            case "storeaddress":
                return "门店地址";
            case "storearea":
                return "门店面积";
            case "decorationtime":
                return "装修时间";
            case "openingdate":
                return "开业时间";
            case "level1":
                return "一级";
            case "level2":
                return "二级";
            case "level3":
                return "三级";
            case "level4":
                return "四级";
            case "contactsr":
                return "林内";
            case "contactshopkeeper":
                return "经销商对接人";
            case "burn":
                return "燃热区域燃热";
            case "burnextend":
                return "燃热区域延米";
            case "electric":
                return "电热区域电热";
            case "electricextend":
                return "电热区域延米";
            case "water":
                return "净水区域净水";
            case "waterextend":
                return "净水区域延米";
            case "kitchen":
                return "厨房区域厨房";
            case "kitchenextend":
                return "厨房区域延米";
            case "heating":
                return "采暖区域采暖";
            case "pumpmultple":
                return "热泵区域两/三联供";
            case "pumpoven":
                return "热泵区域壁挂炉";
            case "pumpextend":
                return "热泵区域延米";
            case "otherextend":
                return "其他类延米数";

            default:
                return string.Empty;
        }
    }

    #endregion


    #region 用名称获取基本信息

    /// <summary>
    /// 获取门店基础信息.
    /// </summary>
    /// <param name="terimal">终端名称.</param>
    /// <returns></returns>
    [HttpGet("GetInfoByTerminal")]
    public async Task<dynamic> GetInfoByTerminal(string terminal)
    {
        var data = await _repository.FirstOrDefaultAsync(x => x.Terminal == terminal);
        if (data is null) return null;
        if (!string.IsNullOrEmpty(data.City))
        {
            var res = data.Adapt<StoresInfoOutput>();
            var target = await _yearTarget.FirstOrDefaultAsync(i =>
                i.Terminal == terminal && i.Year == DateTime.Today.Year.ToString());
            if (target is not null)
            {
                res.saleTarget = target.Target;
            }

            return res;
        }
        else
        {
            var result = new StoresInfoOutput();
            result.id = data.Id;
            result.terminal = data.Terminal;
            result.region = data.Region;
            result.agency = data.Agency;
            result.level1 = data.Level1;
            return result;
        }
    }

    #endregion
}