export default {
  'bm-map': [
    'click',
    'dblclick',
    'rightclick',
    'rightdblclick',
    'maptypechange',
    'mousemove',
    'mouseover',
    'mouseout',
    'movestart',
    'moving',
    'moveend',
    'zoomstart',
    'zoomend',
    'addoverlay',
    'addcontrol',
    'removecontrol',
    'removeoverlay',
    'clearoverlays',
    'dragstart',
    'dragging',
    'dragend',
    'addtilelayer',
    'removetilelayer',
    'load',
    'resize',
    'hotspotclick',
    'hotspotover',
    'hotspotout',
    'tilesloaded',
    'touchstart',
    'touchmove',
    'touchend',
    'longpress'
  ],
  'bm-geolocation': [
    'locationSuccess',
    'locationError'
  ],
  'bm-overview-map': [
    'viewchanged',
    'viewchanging'
  ],
  'bm-marker': [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseout',
    'mouseover',
    'remove',
    'infowindowclose',
    'infowindowopen',
    'dragstart',
    'dragging',
    'dragend',
    'rightclick'
  ],
  'bm-polyline': [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseout',
    'mouseover',
    'remove',
    'lineupdate'
  ],
  'bm-polygon': [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseout',
    'mouseover',
    'remove',
    'lineupdate'
  ],
  'bm-circle': [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseout',
    'mouseover',
    'remove',
    'lineupdate'
  ],
  'bm-label': [
    'click',
    'dblclick',
    'mousedown',
    'mouseup',
    'mouseout',
    'mouseover',
    'remove',
    'rightclick'
  ],
  'bm-info-window': [
    'close',
    'open',
    'maximize',
    'restore',
    'clickclose'
  ],
  'bm-ground': [
    'click',
    'dblclick'
  ],
  'bm-autocomplete': [
    'onconfirm',
    'onhighlight'
  ],
  'bm-point-collection': [
    'click',
    'mouseover',
    'mouseout'
  ]
}
