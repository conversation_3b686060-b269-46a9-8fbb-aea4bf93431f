using JNPF.Common.Core.Manager;
using JNPF.Common.Core.Security;
using JNPF.ClayObject;
using JNPF.Common.Configuration;
using JNPF.Common.Models.NPOI;
using JNPF.DataEncryption;
using JNPF.Common.Enum;
using JNPF.Common.Extension;
using JNPF.Common.Filter;
using JNPF.Common.Security;
using JNPF.DependencyInjection;
using JNPF.DynamicApiController;
using JNPF.FriendlyException;
using JNPF.SubDev.Entitys.Dto.Mydetailscheck;
using JNPF.SubDev.Entitys;
using JNPF.SubDev.Interfaces;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;

namespace JNPF.SubDev;

/// <summary>
/// 业务实现：错误检查.
/// </summary>
[ApiDescriptionSettings(Tag = "SubDev", Name = "Mydetailscheck", Order = 200)]
[Route("api/SubDev/[controller]")]
public class MydetailscheckService : IMydetailscheckService, IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<MydetailscheckEntity> _repository;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    /// <summary>
    /// 用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    /// 初始化一个<see cref="MydetailscheckService"/>类型的新实例.
    /// </summary>
    public MydetailscheckService(
        ISqlSugarRepository<MydetailscheckEntity> mydetailscheckRepository,
        ISqlSugarClient context,
        IUserManager userManager)
    {
        _repository = mydetailscheckRepository;
        _db = context.AsTenant();
        _userManager = userManager;
    }

    /// <summary>
    /// 获取错误检查.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<dynamic> GetInfo(string id)
    {
        return (await _repository.FirstOrDefaultAsync(x => x.Id == id)).Adapt<MydetailscheckInfoOutput>();
    }

    /// <summary>
    /// 获取错误检查列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("")]
    public async Task<dynamic> GetList([FromQuery] MydetailscheckListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<MydetailscheckListOutput>(input.menuId, "id", _userManager.UserOrigin.Equals("pc") ? true : true);
        }

        var data = await _repository.Context.Queryable<MydetailscheckEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.year), it => it.Year.Equals(input.year))
            .WhereIF(!string.IsNullOrEmpty(input.month), it => it.Month.Equals(input.month))
            .WhereIF(!string.IsNullOrEmpty(input.region), it => it.Region.Equals(input.region))
            .WhereIF(!string.IsNullOrEmpty(input.agency), it => it.Agency.Equals(input.agency))
            .WhereIF(!string.IsNullOrEmpty(input.terminal), it => it.Terminal.Contains(input.terminal))
            .WhereIF(!string.IsNullOrEmpty(input.issueType), it => it.IssueType.Contains(input.issueType))
            .WhereIF(!string.IsNullOrEmpty(input.issueDetail), it => it.IssueDetail.Contains(input.issueDetail))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), it =>
                it.Year.Equals(input.keyword)
                || it.Month.Equals(input.keyword)
                || it.Region.Contains(input.keyword)
                || it.Agency.Contains(input.keyword)
                || it.IssueType.Contains(input.keyword)
                || it.IssueDetail.Contains(input.keyword)
                )
            .Where(authorizeWhere)
            .Select(it => new MydetailscheckListOutput
            {
                id = it.Id,
                organizationId = it.OrganizationId,
                creatorTime = it.CreatorTime,
                description = it.Description,
                creatorUserId = it.CreatorUserId,
                year = it.Year,
                month = it.Month,
                region = it.Region,
                agency = it.Agency,
                terminal = it.Terminal,
                issueType = it.IssueType,
                issueDetail = it.IssueDetail,
                level1 = it.Level1,
            })
            .OrderByIF(string.IsNullOrEmpty(input.sidx), it =>
                // 错误严重级别排序：致命(1) > 重大(2) > 普通(3) > 仅提醒(4)
                (it.issueType.Contains("门店检查") && it.issueDetail.Contains("CRM数据中的此门店在基本信息中不存在")) ? 1 :
                (it.issueType.Contains("年度销售目标检查") && it.issueDetail.Contains("年度销售目标没有填写")) ? 1 :
                (it.issueType.Contains("门店邀请人数检查")) ? 1 :
                (it.issueType.Contains("门店检查") && it.issueDetail.Contains("CRM数据中没有此门店")) ? 2 :
                (it.issueType.Contains("电话检查")) ? 3 :
                (it.issueType.Contains("出样数据检查") && it.issueDetail.Contains("没有填写出样信息")) ? 3 :
                (it.issueType.Contains("出样数据检查") && it.issueDetail.Contains("出样数据未填写")) ? 3 :
                (it.issueType.Contains("门店定位检查")) ? 4 : 3
            )
            .OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort)
            .ToPagedListAsync(input.currentPage, input.pageSize);
        return PageResult<MydetailscheckListOutput>.SqlSugarPageResult(data);
    }

    /// <summary>
    /// 新建错误检查.
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("")]
    public async Task Create([FromBody] MydetailscheckCrInput input)
    {
        var entity = input.Adapt<MydetailscheckEntity>();
        entity.Id = SnowflakeIdHelper.NextId();
        entity.OrganizationId = _userManager.User.OrganizeId;
        entity.CreatorTime = DateTime.Now;
        entity.CreatorUserId = _userManager.UserId;
        var isOk = await _repository.Context.Insertable(entity).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1000);
    }

    /// <summary>
    /// 获取错误检查无分页列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    private async Task<dynamic> GetNoPagingList([FromQuery] MydetailscheckListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<MydetailscheckListOutput>(input.menuId, "id", _userManager.UserOrigin.Equals("pc") ? true : true);
        }

        return await _repository.Context.Queryable<MydetailscheckEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.year), it => it.Year.Equals(input.year))
            .WhereIF(!string.IsNullOrEmpty(input.month), it => it.Month.Equals(input.month))
            .WhereIF(!string.IsNullOrEmpty(input.region), it => it.Region.Equals(input.region))
            .WhereIF(!string.IsNullOrEmpty(input.agency), it => it.Agency.Equals(input.agency))
            .WhereIF(!string.IsNullOrEmpty(input.terminal), it => it.Terminal.Contains(input.terminal))
            .WhereIF(!string.IsNullOrEmpty(input.issueType), it => it.IssueType.Contains(input.issueType))
            .WhereIF(!string.IsNullOrEmpty(input.issueDetail), it => it.IssueDetail.Contains(input.issueDetail))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), it =>
                it.Year.Equals(input.keyword)
                || it.Month.Equals(input.keyword)
                || it.Region.Contains(input.keyword)
                || it.Agency.Contains(input.keyword)
                || it.IssueType.Contains(input.keyword)
                || it.IssueDetail.Contains(input.keyword)
                )
            .Where(authorizeWhere)
            .OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.Id)
            .Select(it => new MydetailscheckListOutput
            {
                id = it.Id,
                organizationId = it.OrganizationId,
                creatorTime = it.CreatorTime,
                description = it.Description,
                creatorUserId = it.CreatorUserId,
                year = it.Year,
                month = it.Month,
                region = it.Region,
                agency = it.Agency,
                terminal = it.Terminal,
                issueType = it.IssueType,
                issueDetail = it.IssueDetail,
                level1 = it.Level1,
            }).OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort).ToListAsync();
    }

    /// <summary>
    /// 导出错误检查.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("Actions/Export")]
    public async Task<dynamic> Export([FromQuery] MydetailscheckListQueryInput input)
    {
        var exportData = new List<MydetailscheckListOutput>();
        if (input.dataType == 0)
            exportData = Clay.Object(await GetList(input)).Solidify<PageResult<MydetailscheckListOutput>>().list;
        else
            exportData = await GetNoPagingList(input);
        List<ParamsModel> paramList = "[{\"value\":\"年度\",\"field\":\"year\"},{\"value\":\"月份\",\"field\":\"month\"},{\"value\":\"大区\",\"field\":\"region\"},{\"value\":\"办事处\",\"field\":\"agency\"},{\"value\":\"终端名称\",\"field\":\"terminal\"},{\"value\":\"错误类型\",\"field\":\"issueType\"},{\"value\":\"错误详情\",\"field\":\"issueDetail\"},{\"value\":\"创建时间\",\"field\":\"creatorTime\"},{\"value\":\"创建用户\",\"field\":\"creatorUserId\"},{\"value\":\"备注\",\"field\":\"description\"},{\"value\":\"组织id\",\"field\":\"organizationId\"},]".ToList<ParamsModel>();
        ExcelConfig excelconfig = new ExcelConfig();
        excelconfig.FileName = "错误检查.xls";
        excelconfig.HeadFont = "微软雅黑";
        excelconfig.HeadPoint = 10;
        excelconfig.IsAllSizeColumn = true;
        excelconfig.ColumnModel = new List<ExcelColumnModel>();
        foreach (var item in input.selectKey.Split(',').ToList())
        {
            var isExist = paramList.Find(p => p.field == item);
            if (isExist != null)
                excelconfig.ColumnModel.Add(new ExcelColumnModel() { Column = isExist.field, ExcelColumn = isExist.value });
        }

        var addPath = FileVariable.TemporaryFilePath + excelconfig.FileName;
        ExcelExportHelper<MydetailscheckListOutput>.Export(exportData, excelconfig, addPath);
        var fileName = _userManager.UserId + "|" + addPath + "|xls";
        return new
        {
            name = excelconfig.FileName,
            url = "/api/File/Download?encryption=" + DESCEncryption.Encrypt(fileName, "JNPF")
        };
    }

    /// <summary>
    /// 批量删除错误检查.
    /// </summary>
    /// <param name="ids">主键数组.</param>
    /// <returns></returns>
    [HttpPost("batchRemove")]
    public async Task BatchRemove([FromBody] List<string> ids)
    {
        var entitys = await _repository.Context.Queryable<MydetailscheckEntity>().In(it => it.Id, ids).ToListAsync();
        if (entitys.Count > 0)
        {
            try
            {
                // 开启事务
                _db.BeginTran();

                // 批量删除错误检查
                await _repository.Context.Deleteable<MydetailscheckEntity>().In(it => it.Id, ids).ExecuteCommandAsync();

                // 关闭事务
                _db.CommitTran();
            }
            catch (Exception)
            {
                // 回滚事务
                _db.RollbackTran();

                throw Oops.Oh(ErrorCode.COM1002);
            }
        }
    }

    /// <summary>
    /// 更新错误检查.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task Update(string id, [FromBody] MydetailscheckUpInput input)
    {
        var entity = input.Adapt<MydetailscheckEntity>();
        var isOk = await _repository.Context.Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1001);
    }

    /// <summary>
    /// 删除错误检查.
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task Delete(string id)
    {
        var isOk = await _repository.Context.Deleteable<MydetailscheckEntity>().Where(it => it.Id.Equals(id)).ExecuteCommandAsync();
        if (!(isOk > 0)) throw Oops.Oh(ErrorCode.COM1002);
    }
}