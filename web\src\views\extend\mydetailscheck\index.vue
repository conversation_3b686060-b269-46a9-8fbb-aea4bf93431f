<template>
    <div class="JNPF-common-layout">
        <div class="JNPF-common-layout-center">
            <el-row class="JNPF-common-search-box" :gutter="16">
                <el-form @submit.native.prevent="">
                    <el-col :span="6">
                        <el-form-item label="年度">
                            <el-input v-model="query.year" placeholder="年度" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="月份">
                            <el-input v-model="query.month" placeholder="月份" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="大区">
                            <el-select v-model="query.region" placeholder="大区" clearable>
                                <el-option v-for="(item, index) in regionOptions" :key="index" :label="item.region"
                                    :value="item.region" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="终端门店">
                            <el-input v-model="query.terminal" placeholder="终端门店" clearable />
                        </el-form-item>
                    </el-col>
                    <template v-if="showAll">
                        <el-col :span="6">
                            <el-form-item label="办事处">
                                <el-select v-model="query.agency" placeholder="办事处" clearable>
                                    <el-option v-for="(item, index) in agencyOptions" :key="index" :label="item.agency"
                                        :value="item.agency" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="错误类型">
                                <el-input v-model="query.issueType" placeholder="错误类型" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="错误详情">
                                <el-input v-model="query.issueDetail" placeholder="错误详情" clearable />
                            </el-form-item>
                        </el-col>
                    </template>
                    <el-col :span="6">
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" @click="search()">查询</el-button>



                            <el-button icon="el-icon-refresh-right" @click="reset()">重置</el-button>
                            <el-button type="text" icon="el-icon-arrow-down" @click="showAll = true"
                                v-if="!showAll">展开</el-button>
                            <el-button type="text" icon="el-icon-arrow-up" @click="showAll = false"
                                v-else>收起</el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <div class="JNPF-common-layout-main JNPF-flex-main">
                <div class="JNPF-common-head">
                    <div>
                        <el-button type="primary" icon="el-icon-plus" @click="addOrUpdateHandle()"
                            v-has="'btn_add'">新增</el-button>
                        <el-button type="text" icon="el-icon-download" @click="exportData()"
                            v-has="'btn_download'">导出</el-button>
                        <el-button type="text" icon="el-icon-delete" @click="handleBatchRemoveDel()"
                            v-has="'btn_batchRemove'">批量删除</el-button>
                        <!-- 弹出对话框 -->
                        <el-popover placement="top" width="160px" v-model="visible" popper-class="popper">
                            <p>请选择门店类型</p>
                            <div style="text-align: right; margin: 10px;">
                                <el-button size="mini" type="warning" @click="detailCheck('生活馆')">生活馆</el-button>
                                <el-button type="primary" size="mini" @click="detailCheck('两通店')">两通店</el-button>
                            </div>
                            <el-button slot="reference" style=" margin-left: 10px;margin-right: 10px;" type="primary"
                                v-has="'btn_detailsCheck'">错误扫描</el-button>
                        </el-popover>

                    </div>
                    <div class="JNPF-common-head-right">
                        <el-tooltip effect="dark" content="刷新" placement="top">
                            <el-link icon="icon-ym icon-ym-Refresh JNPF-common-head-icon" :underline="false"
                                @click="reset()" />
                        </el-tooltip>
                    </div>
                </div>
                <JNPF-table v-loading="listLoading" :data="list" has-c @selection-change="handleSelectionChange">
                    <el-table-column prop="year" label="年度" align="left" width="100px" />
                    <el-table-column prop="month" label="月份" align="left" width="100px" />
                    <el-table-column label="大区" prop="region" align="left" width="120px">
                        <template slot-scope="scope">{{ scope.row.region | dynamicText(regionOptions) }}</template>
                    </el-table-column>
                    <el-table-column label="办事处" prop="agency" align="left" width="120px">
                        <template slot-scope="scope">{{ scope.row.agency | dynamicText(agencyOptions) }}</template>
                    </el-table-column>
                    <el-table-column prop="level1" label="门店属性" align="left" width="120px" />
                    <el-table-column prop="terminal" label="终端名称" align="left" width="300px" />
                    <el-table-column label="错误级别" align="left" width="120px">
                        <template slot-scope="scope">
                            <el-tag :type="getErrorLevelType(scope.row)" size="small">
                                {{ getErrorLevel(scope.row) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="issueType" label="错误类型" align="left" width="120px" />
                    <el-table-column prop="issueDetail" label="错误详情" align="left" width="300px" />
                    
                    <!-- <el-table-column prop="creatorTime" label="创建时间" align="left" :formatter="jnpf.tableDateFormatShort"/> -->
                    <!-- <el-table-column prop="creatorUserId" label="创建用户" align="left" /> -->
                    <!-- <el-table-column prop="description" label="备注" align="left" /> -->
                    <!-- <el-table-column prop="organizationId" label="组织id" align="left" /> -->
                    <el-table-column label="操作" fixed="right" width="100">
                        <template slot-scope="scope">
                            <el-button type="text" @click="addOrUpdateHandle(scope.row.id)"
                                v-has="'btn_edit'">编辑</el-button>
                            <el-button type="text" @click="handleDel(scope.row.id)" class='JNPF-table-delBtn'
                                v-has="'btn_remove'">删除</el-button>
                        </template>
                    </el-table-column>
                </JNPF-table>
                <pagination :total="total" :page.sync="listQuery.currentPage" :limit.sync="listQuery.pageSize"
                    @pagination="initData" />
            </div>
        </div>
        <JNPF-Form v-if="formVisible" ref="JNPFForm" @refresh="refresh" />
        <ExportBox v-if="exportBoxVisible" ref="ExportBox" @download="download" />


    </div>
</template>
<script>
import request from '@/utils/request'
import { getDataInterfaceRes } from '@/api/systemData/dataInterface'
import JNPFForm from './Form'
import ExportBox from './ExportBox'
export default {
    components: { JNPFForm, ExportBox },
    data() {
        return {
            visible: false,
            showAll: false,
            query: {
                year: undefined,
                month: undefined,
                region: undefined,
                agency: undefined,
                terminal: undefined,
                issueType: undefined,
                issueDetail: undefined,
            },
            list: [],
            listLoading: true,
            multipleSelection: [],
            total: 0,
            listQuery: {
                currentPage: 1,
                pageSize: 20,
                sort: "desc",
                sidx: "",
            },
            formVisible: false,
            exportBoxVisible: false,
            columnList: [
                { prop: 'year', label: '年度' },
                { prop: 'month', label: '月份' },
                { prop: 'region', label: '大区' },
                { prop: 'agency', label: '办事处' },
                { prop: 'terminal', label: '终端名称' },
                { prop: 'issueType', label: '错误类型' },
                { prop: 'issueDetail', label: '错误详情' },
                { prop: 'errorLevel', label: '错误级别' },
                { prop: 'creatorTime', label: '创建时间' },
                { prop: 'creatorUserId', label: '创建用户' },
                { prop: 'description', label: '备注' },
                { prop: 'organizationId', label: '组织id' },
            ],
            regionOptions: [],
            agencyOptions: [],
        }
    },
    computed: {
        menuId() {
            return this.$route.meta.modelId || ''
        }
    },
    created() {
        this.initData()
        this.getregionOptions();
        this.getagencyOptions();
    },
    methods: {
        detailCheck(storeType) {
            // console.log(storeType)
            this.$message({
                type: 'warning',
                message: "即将进行数据检查，请耐心等待",
            });
            this.visible = false
            request({
                url: `/api/SubDev/mystoreselldata/Actions/DetailsCheckAsync`,
                method: 'GET',
                data: { storeType }
            }).then(res => {
                this.$message({
                    type: 'success',
                    message: res.msg,
                    onClose: () => {
                        this.initData()
                    }
                });
            })
        },
        getregionOptions() {
            getDataInterfaceRes('521907587122250309').then(res => {
                let data = res.data.data
                this.regionOptions = data
            });
        },
        getagencyOptions() {
            getDataInterfaceRes('521907587122250309').then(res => {
                let data = res.data.data
                this.agencyOptions = data
            });
        },
        initData() {
            this.listLoading = true;
            let _query = {
                ...this.listQuery,
                ...this.query,
                menuId: this.menuId,
            };
            let query = {}
            for (let key in _query) {
                if (Array.isArray(_query[key])) {
                    query[key] = _query[key].join()
                } else {
                    query[key] = _query[key]
                }
            }
            request({
                url: `/api/SubDev/Mydetailscheck`,
                method: 'GET',
                data: query
            }).then(res => {
                this.list = res.data.list
                this.total = res.data.pagination.total
                this.listLoading = false
            })
        },
        handleDel(id) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                type: 'warning'
            }).then(() => {
                request({
                    url: `/api/SubDev/Mydetailscheck/${id}`,
                    method: 'DELETE'
                }).then(res => {
                    this.$message({
                        type: 'success',
                        message: res.msg,
                        onClose: () => {
                            this.initData()
                        }
                    });
                })
            }).catch(() => {
            });
        },
        handleSelectionChange(val) {
            const res = val.map(item => item.id)
            this.multipleSelection = res
        },
        handleBatchRemoveDel() {
            if (!this.multipleSelection.length) {
                this.$message({
                    type: 'error',
                    message: '请选择一条数据',
                    duration: 1500,
                })
                return
            }
            const ids = this.multipleSelection
            this.$confirm('您确定要删除这些数据吗, 是否继续？', '提示', {
                type: 'warning'
            }).then(() => {
                request({
                    url: `/api/SubDev/Mydetailscheck/batchRemove`,
                    method: 'POST',
                    data: ids,
                }).then(res => {
                    this.$message({
                        type: 'success',
                        message: res.msg,
                        onClose: () => {
                            this.initData()
                        }
                    });
                })
            }).catch(() => { })
        },
        addOrUpdateHandle(id) {
            this.formVisible = true
            this.$nextTick(() => {
                this.$refs.JNPFForm.init(id)
            })
        },
        exportData() {
            this.exportBoxVisible = true
            this.$nextTick(() => {
                this.$refs.ExportBox.init(this.columnList)
            })
        },
        download(data) {
            let query = { ...data, ...this.listQuery, ...this.query, menuId: this.menuId }
            
            console.log('=== 导出请求开始 ===')
            console.log('请求参数:', query)
            console.log('请求URL:', `/api/SubDev/Mydetailscheck/Actions/Export`)
            
            request({
                url: `/api/SubDev/Mydetailscheck/Actions/Export`,
                method: 'GET',
                data: query
            }).then(res => {
                console.log('=== 后端响应 ===')
                console.log('完整响应:', res)
                console.log('响应数据:', res.data)
                console.log('文件名:', res.data.name)
                console.log('文件URL:', res.data.url)
                console.log('URL类型:', typeof res.data.url)
                console.log('URL长度:', res.data.url ? res.data.url.length : 'undefined')
                
                if (!res.data.url) return
                
                console.log('=== 开始下载 ===')
                console.log('最终下载URL:', res.data.url)
                
                // 下载文件
                const link = document.createElement('a')
                link.href = res.data.url
                link.download = res.data.name || '错误检查.xls'
                console.log('创建的下载链接:', link.href)
                console.log('下载文件名:', link.download)
                
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                
                console.log('✅ 下载链接已触发')
                
                this.$refs.ExportBox.visible = false
                this.exportBoxVisible = false
            })
        },
        search() {
            this.listQuery = {
                currentPage: 1,
                pageSize: 20,
                sort: "desc",
                sidx: "creatorTime",
            }
            this.initData()
        },
        refresh(isrRefresh) {
            this.formVisible = false
            if (isrRefresh) this.reset()
        },
        reset() {
            for (let key in this.query) {
                this.query[key] = undefined
            }
            this.listQuery = {
                currentPage: 1,
                pageSize: 20,
                sort: "desc",
                sidx: "creatorTime",
            }
            this.initData()
        },
        // 获取错误级别
        getErrorLevel(row) {
            const issueType = row.issueType || ''
            const issueDetail = row.issueDetail || ''

            // 致命级别 - 影响核心业务流程
            if (issueType.includes('门店检查') && issueDetail.includes('CRM数据中的此门店在基本信息中不存在')) {
                return '致命'
            }
            if (issueType.includes('年度销售目标检查') && issueDetail.includes('年度销售目标没有填写')) {
                return '致命'
            }
            // 门店邀请人数检查 - 改为致命级别
            if (issueType.includes('门店邀请人数检查')) {
                return '致命'
            }

            // 重大级别 - 影响重要业务功能
            if (issueType.includes('门店检查') && issueDetail.includes('CRM数据中没有此门店')) {
                return '重大'
            }

            // 普通级别 - 影响数据完整性
            if (issueType.includes('电话检查')) {
                return '普通'
            }
            if (issueType.includes('出样数据检查') && issueDetail.includes('没有填写出样信息')) {
                return '普通'
            }
            // 出样数据未填写 - 改为普通级别
            if (issueType.includes('出样数据检查') && issueDetail.includes('出样数据未填写')) {
                return '普通'
            }

            // 仅提醒级别 - 不影响核心功能
            if (issueType.includes('门店定位检查')) {
                return '仅提醒'
            }

            // 默认为普通级别
            return '普通'
        },
        // 获取错误级别对应的Element UI标签类型
        getErrorLevelType(row) {
            const level = this.getErrorLevel(row)
            switch (level) {
                case '致命':
                    return 'danger'  // 红色
                case '重大':
                    return 'warning' // 橙色
                case '普通':
                    return ''        // 黄色（默认）
                case '仅提醒':
                    return 'info'    // 蓝色
                default:
                    return ''
            }
        }
    }
}
</script>
<style lang="scss" scoped></style>